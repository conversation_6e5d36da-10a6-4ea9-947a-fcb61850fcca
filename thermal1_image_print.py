#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
thermal1_image_print.py - وحدة الطباعة الحرارية المحسنة
تم تطويرها للعمل بالطباعة عبر ملف نصي بدلاً من تحويل النص إلى صورة
لتحسين جودة الطباعة وسرعتها وتوافقها مع جميع أنواع الطابعات

الميزات الجديدة:
- طباعة نصية مباشرة بدون تحويل إلى صورة
- دعم أفضل للنصوص العربية
- سرعة أكبر في الطباعة
- توافق مع جميع أنواع الطابعات
- جودة طباعة محسنة

تاريخ الإنشاء: 2025-01-27
آخر تحديث: 2025-01-27
"""

import os
import sys
import datetime
import sqlite3
import traceback
import tempfile
import subprocess
import time

def get_thermal_printer_name():
    """الحصول على اسم الطابعة من قاعدة البيانات أو الطابعة الافتراضية"""
    try:
        # محاولة الحصول على الطابعة من قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            print(f"تم العثور على طابعة محددة في قاعدة البيانات: {result[0]}")
            return result[0]
        else:
            print("لم يتم تحديد طابعة في قاعدة البيانات، البحث عن الطابعة الافتراضية...")
    except Exception as e:
        print(f"خطأ في الحصول على الطابعة من قاعدة البيانات: {e}")

    # محاولة الحصول على الطابعة الافتراضية
    try:
        if os.name == 'nt':  # Windows
            try:
                import win32print
                default_printer = win32print.GetDefaultPrinter()
                if default_printer:
                    print(f"تم العثور على الطابعة الافتراضية: {default_printer}")
                    return default_printer
            except ImportError:
                print("مكتبة win32print غير متوفرة، محاولة طرق أخرى...")
            
            # طريقة بديلة باستخدام wmic
            try:
                result = subprocess.run(['wmic', 'printer', 'get', 'name'], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # تخطي العنوان
                        printer_name = line.strip()
                        if printer_name and printer_name != "Name":
                            print(f"تم العثور على طابعة: {printer_name}")
                            return printer_name
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")
        
        # للأنظمة الأخرى (Linux/Mac)
        else:
            try:
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.startswith('printer'):
                            printer_name = line.split()[1]
                            print(f"تم العثور على طابعة: {printer_name}")
                            return printer_name
            except Exception as e:
                print(f"خطأ في الحصول على قائمة الطابعات: {e}")
    
    except Exception as e:
        print(f"خطأ عام في الحصول على اسم الطابعة: {e}")
    
    print("لم يتم العثور على أي طابعة متاحة")
    return None

def get_institution_info():
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    institution_name = "المؤسسة التعليمية"
    school_year = "2024/2025"
    guard_number = "1"
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على بيانات المؤسسة
        cursor.execute("SELECT المؤسسة, السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year
            guard_number = str(result[2]) if result[2] else guard_number
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ في الحصول على معلومات المؤسسة: {e}")
    
    return institution_name, school_year, guard_number

def direct_print_text(content, printer_name=None):
    """طباعة النص مباشرة إلى الطابعة"""
    if not printer_name:
        printer_name = get_thermal_printer_name()
    
    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False
    
    try:
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(content)
            file_path = f.name
        
        print(f"تم إنشاء ملف مؤقت للطباعة: {file_path}")
        print(f"محتوى الطباعة:\n{'-'*50}")
        print(content)
        print(f"{'-'*50}")
        
        success = False
        
        # طباعة الملف حسب نظام التشغيل
        if os.name == 'nt':  # Windows
            try:
                # محاولة استخدام win32api
                import win32api
                win32api.ShellExecute(0, "printto", file_path, f'"{printer_name}"', ".", 0)
                print(f"تم إرسال الطباعة إلى {printer_name} باستخدام win32api")
                success = True
            except ImportError:
                try:
                    # استخدام أمر print في Windows
                    subprocess.run(f'print /d:"{printer_name}" "{file_path}"', shell=True, check=True)
                    print(f"تم إرسال الطباعة باستخدام أمر print")
                    success = True
                except subprocess.CalledProcessError:
                    try:
                        # استخدام notepad للطباعة
                        subprocess.run(f'notepad /p "{file_path}"', shell=True)
                        print("تم إرسال الطباعة باستخدام notepad")
                        success = True
                    except Exception as e:
                        print(f"خطأ في الطباعة: {e}")
        else:  # Linux/Mac
            try:
                subprocess.run(["lp", "-d", printer_name, file_path], check=True)
                print(f"تم إرسال الطباعة باستخدام lp")
                success = True
            except subprocess.CalledProcessError as e:
                print(f"خطأ في الطباعة: {e}")
        
        # انتظار قصير ثم حذف الملف المؤقت
        time.sleep(3)
        try:
            os.unlink(file_path)
            print("تم حذف الملف المؤقت")
        except:
            print("تعذر حذف الملف المؤقت")
        
        return success
        
    except Exception as e:
        print(f"خطأ عام في الطباعة: {e}")
        return False

def format_entry_form_content(students, section, date_str, time_str):
    """تنسيق محتوى ورقة الدخول بشكل محسن"""
    institution_name, school_year, guard_number = get_institution_info()
    
    # بناء محتوى الطباعة
    content = ""
    
    # عنوان المؤسسة مع تنسيق محسن
    content += f"\n{institution_name}\n"
    content += f"السنة الدراسية: {school_year}\n"
    content += f"حراسة رقم: {guard_number}\n"
    content += "=" * 45 + "\n"
    content += "        ورقة السماح بالدخول        \n"
    content += "=" * 45 + "\n\n"
    
    # معلومات التاريخ والوقت
    content += f"التاريخ: {date_str}        الوقت: {time_str}\n"
    content += f"القسم: {section}\n"
    content += "-" * 45 + "\n"
    
    # عنوان الجدول مع تنسيق أفضل
    content += f"{'ر.ت':<8} {'الاسم الكامل':<30}\n"
    content += "-" * 45 + "\n"
    
    # بيانات التلاميذ مع تنسيق محسن
    for i, student in enumerate(students, 1):
        if isinstance(student, dict):
            rt = student.get('rt', str(i))
            name = student.get('name', 'غير محدد')
            code = student.get('code', '')
        else:
            rt = str(i)
            name = str(student)
            code = ''
        
        # تنسيق السطر مع محاذاة
        content += f"{rt:<8} {name:<30}\n"
    
    content += "-" * 45 + "\n"
    content += f"عدد التلاميذ: {len(students)}\n"
    content += "\n"
    
    # ملاحظات مع تنسيق أفضل
    content += "ملاحظات:\n"
    content += "• يُسمح للتلميذ بالدخول بعد تسجيل وصوله\n"
    content += "• يجب على التلميذ التوجه مباشرة إلى قسمه\n"
    content += "• يُرجى الاحتفاظ بهذه الورقة\n"
    content += "\n"
    content += "توقيع المسؤول: ________________\n"
    content += "\n"
    content += f"تاريخ الطباعة: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    content += "\n\n\n"  # مساحة إضافية للقطع
    
    return content

def print_entry_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة الدخول مباشرة بالطريقة النصية المحسنة"""
    try:
        # تحديد التاريخ والوقت إذا لم يتم تمريرهما
        if not date_str:
            date_str = datetime.datetime.now().strftime("%Y/%m/%d")
        if not time_str:
            time_str = datetime.datetime.now().strftime("%H:%M")
        
        print("=" * 60)
        print("بدء طباعة ورقة الدخول بالطريقة النصية المحسنة...")
        print("=" * 60)
        print(f"عدد التلاميذ: {len(students)}")
        print(f"القسم: {section}")
        print(f"التاريخ: {date_str}")
        print(f"الوقت: {time_str}")
        
        # التحقق من وجود تلاميذ
        if not students:
            print("تحذير: لا يوجد تلاميذ للطباعة")
            return False
        
        # تنسيق المحتوى
        content = format_entry_form_content(students, section, date_str, time_str)
        
        # طباعة المحتوى
        success = direct_print_text(content)
        
        if success:
            print("✅ تمت طباعة ورقة الدخول بنجاح")
            print("📄 تم استخدام الطباعة النصية المحسنة")
        else:
            print("❌ فشلت طباعة ورقة الدخول")
        
        print("=" * 60)
        return success

    except Exception as e:
        print(f"❌ خطأ في طباعة ورقة الدخول: {e}")
        traceback.print_exc()
        return False

def format_late_form_content(students, section, date_str, time_str):
    """تنسيق محتوى ورقة التأخر"""
    institution_name, school_year, guard_number = get_institution_info()

    # بناء محتوى الطباعة
    content = ""

    # عنوان المؤسسة
    content += f"\n{institution_name}\n"
    content += f"السنة الدراسية: {school_year}\n"
    content += f"حراسة رقم: {guard_number}\n"
    content += "=" * 45 + "\n"
    content += "           ورقة التأخر           \n"
    content += "=" * 45 + "\n\n"

    # معلومات التاريخ والوقت
    content += f"التاريخ: {date_str}        وقت الوصول: {time_str}\n"
    content += f"القسم: {section}\n"
    content += "-" * 45 + "\n"

    # عنوان الجدول
    content += f"{'ر.ت':<8} {'الاسم الكامل':<30}\n"
    content += "-" * 45 + "\n"

    # بيانات التلاميذ
    for i, student in enumerate(students, 1):
        if isinstance(student, dict):
            rt = student.get('rt', str(i))
            name = student.get('name', 'غير محدد')
        else:
            rt = str(i)
            name = str(student)

        content += f"{rt:<8} {name:<30}\n"

    content += "-" * 45 + "\n"
    content += f"عدد التلاميذ المتأخرين: {len(students)}\n"
    content += "\n"
    content += "ملاحظات:\n"
    content += "• تم تسجيل التأخر في السجل\n"
    content += "• يجب على التلميذ تبرير سبب التأخر\n"
    content += "• يُرجى مراجعة إدارة المؤسسة\n"
    content += "\n"
    content += "توقيع المسؤول: ________________\n"
    content += "\n"
    content += f"تاريخ الطباعة: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    content += "\n\n\n"

    return content

def print_late_form_direct(students, section, date_str=None, time_str=None):
    """طباعة ورقة التأخر مباشرة"""
    try:
        # تحديد التاريخ والوقت إذا لم يتم تمريرهما
        if not date_str:
            date_str = datetime.datetime.now().strftime("%Y/%m/%d")
        if not time_str:
            time_str = datetime.datetime.now().strftime("%H:%M")

        print("=" * 60)
        print("بدء طباعة ورقة التأخر...")
        print("=" * 60)
        print(f"عدد التلاميذ: {len(students)}")
        print(f"القسم: {section}")

        # التحقق من وجود تلاميذ
        if not students:
            print("تحذير: لا يوجد تلاميذ للطباعة")
            return False

        # تنسيق المحتوى
        content = format_late_form_content(students, section, date_str, time_str)

        # طباعة المحتوى
        success = direct_print_text(content)

        if success:
            print("✅ تمت طباعة ورقة التأخر بنجاح")
        else:
            print("❌ فشلت طباعة ورقة التأخر")

        print("=" * 60)
        return success

    except Exception as e:
        print(f"❌ خطأ في طباعة ورقة التأخر: {e}")
        traceback.print_exc()
        return False

def test_printer():
    """اختبار الطابعة بطباعة صفحة تجريبية"""
    try:
        print("=" * 60)
        print("اختبار الطابعة...")
        print("=" * 60)

        # محتوى الاختبار
        test_content = """
اختبار الطابعة
===============

هذه صفحة اختبار للطابعة الحرارية
تم إنشاؤها بواسطة النظام المحسن للطباعة النصية

معلومات الاختبار:
- التاريخ: """ + datetime.datetime.now().strftime("%Y/%m/%d") + """
- الوقت: """ + datetime.datetime.now().strftime("%H:%M:%S") + """
- النظام: Windows
- الترميز: UTF-8

إذا ظهر هذا النص بوضوح، فإن الطابعة تعمل بشكل صحيح.

===============
انتهى الاختبار
===============


"""

        # طباعة المحتوى
        success = direct_print_text(test_content)

        if success:
            print("✅ تم إرسال صفحة الاختبار بنجاح")
            print("📄 تحقق من الطابعة للتأكد من جودة الطباعة")
        else:
            print("❌ فشل في إرسال صفحة الاختبار")

        print("=" * 60)
        return success

    except Exception as e:
        print(f"❌ خطأ في اختبار الطابعة: {e}")
        return False

# دالة للتوافق مع الكود القديم
def print_entry_form_image(*args, **kwargs):
    """دالة للتوافق مع الكود القديم - تحويل إلى الطباعة النصية"""
    print("تحذير: تم استدعاء الدالة القديمة، سيتم التحويل إلى الطباعة النصية المحسنة")
    return print_entry_form_direct(*args, **kwargs)

if __name__ == "__main__":
    print("🖨️ وحدة الطباعة الحرارية المحسنة")
    print("=" * 60)
    print("الميزات:")
    print("✅ طباعة نصية مباشرة بدون تحويل إلى صورة")
    print("✅ دعم محسن للنصوص العربية")
    print("✅ سرعة أكبر في الطباعة")
    print("✅ توافق مع جميع أنواع الطابعات")
    print("✅ جودة طباعة محسنة")
    print("=" * 60)

    # اختبار الطابعة
    test_printer()
