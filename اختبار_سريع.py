#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للقائمة المنسدلة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def quick_test():
    """اختبار سريع"""
    print("🚀 اختبار سريع للقائمة المنسدلة")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    try:
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        main_window.show()
        main_window.setWindowTitle("اختبار سريع - انقر على تبويب بيانات المؤسسة")
        
        print("\n📋 تعليمات:")
        print("1. ابحث عن تبويب 'بيانات المؤسسة'")
        print("2. انقر عليه")
        print("3. راقب الرسائل في وحدة التحكم")
        print("4. ابحث عن القائمة المنسدلة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = quick_test()
    print(f"\nانتهى الاختبار: {exit_code}")
    sys.exit(exit_code)
