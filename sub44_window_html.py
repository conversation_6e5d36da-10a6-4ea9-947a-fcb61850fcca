# sub44_window_html.py
# ملف مساعد لوظائف أزرار نافذة الحراسة العامة - الواجهة الحديثة
# يحتوي على جميع الوظائف البرمجية للأزرار المستخرجة من sub4_window.py

import sys
import os
import sqlite3
import datetime
import importlib.util
import importlib
import pandas as pd
import json
from PyQt5.QtCore import Qt, QDate, QTime, QModelIndex, pyqtSlot, QSize, QItemSelection, QTimer
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
    QPushButton, QDateEdit, QTimeEdit, QTableView, QFrame,
    QGraphicsDropShadowEffect, QAbstractItemView, QHeaderView,
    QSizePolicy, Q<PERSON><PERSON>l, QStyledItemDelegate, QMessageBox, QCheckBox,
    QStyle, QStyleOptionButton, QDialog, QTextBrowser, QListWidget,
    QListWidgetItem, QLineEdit, QRadioButton, QButtonGroup, QFileDialog,
    QGroupBox, QGridLayout, QScrollArea, QTableWidget, QTableWidgetItem, QToolTip
)
from PyQt5.QtSql import QSqlDatabase, QSqlQueryModel, QSqlQuery
from PyQt5.QtGui import QPixmap, QFont, QIcon, QColor, QPalette

class GuardWindowFunctions:
    """فئة تحتوي على جميع وظائف أزرار نافذة الحراسة العامة"""
    
    def __init__(self, parent_window):
        """
        تهيئة فئة الوظائف
        parent_window: النافذة الأصلية sub4_window_html
        """
        self.parent = parent_window
        self.db = None
        self.current_academic_year = None
        self.current_date = QDate.currentDate()
        self.current_time = QTime.currentTime()
        self.use_custom_datetime = False
        
        # تهيئة اتصال قاعدة البيانات
        self.init_database()
        
        # استيراد الوحدات المطلوبة
        self.import_required_modules()
    
    def init_database(self):
        """تهيئة اتصال قاعدة البيانات"""
        try:
            # استخدام اتصال قاعدة البيانات من النافذة الأصلية إذا كان متاحاً
            if hasattr(self.parent, 'db') and self.parent.db:
                self.db = self.parent.db
                self.current_academic_year = self.parent.current_academic_year
                return True
            
            # إنشاء اتصال جديد إذا لم يكن متاحاً
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")
            
            # إنشاء اتصال قاعدة البيانات
            self.db = QSqlDatabase.addDatabase("QSQLITE", "guard_functions_connection")
            self.db.setDatabaseName(db_path)
            
            if not self.db.open():
                print(f"خطأ في فتح قاعدة البيانات: {self.db.lastError().text()}")
                return False
                
            # الحصول على السنة الدراسية الحالية
            self.get_current_academic_year()
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            return False
    
    def get_current_academic_year(self):
        """الحصول على السنة الدراسية الحالية من قاعدة البيانات"""
        try:
            query = QSqlQuery(db=self.db)
            if query.exec_("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1"):
                if query.next():
                    self.current_academic_year = query.value(0)
                else:
                    self.current_academic_year = "2024-2025"  # قيمة افتراضية
            else:
                self.current_academic_year = "2024-2025"  # قيمة افتراضية
        except Exception as e:
            print(f"خطأ في الحصول على السنة الدراسية: {str(e)}")
            self.current_academic_year = "2024-2025"  # قيمة افتراضية
    
    def import_required_modules(self):
        """استيراد الوحدات البرمجية المطلوبة"""
        try:
            # محاولة استيراد وحدات الطباعة
            self.import_print_modules()
            
            # محاولة استيراد وحدات أخرى
            self.import_other_modules()
            
        except Exception as e:
            print(f"خطأ في استيراد الوحدات: {str(e)}")
    
    def import_print_modules(self):
        """استيراد وحدات الطباعة"""
        try:
            # استيراد وحدات الطباعة الحرارية
            self.thermal_image_print = self.import_module_by_name("thermal_image_print")
            if self.thermal_image_print:
                self.print_entry_form_direct = getattr(self.thermal_image_print, "print_entry_form_direct", self.dummy_print)
                self.print_late_form_direct = getattr(self.thermal_image_print, "print_late_form_direct", self.dummy_print)
            else:
                self.print_entry_form_direct = self.dummy_print
                self.print_late_form_direct = self.dummy_print
            
            # استيراد وحدات طباعة النماذج من print_test.py
            self.print_test_module = self.import_module_by_name("print_test")
            if self.print_test_module:
                self.print_entry_form = getattr(self.print_test_module, "print_entry_form", self.dummy_print)
                self.print_doctor_visit_form = getattr(self.print_test_module, "print_doctor_visit_form", self.dummy_print)
                self.print_late_form = getattr(self.print_test_module, "print_late_form", self.dummy_print)
                self.print_guidance_form = getattr(self.print_test_module, "print_guidance_form", self.dummy_print)
                self.print_permission_form = getattr(self.print_test_module, "print_permission_form", self.dummy_print)
                self.print_secret_codes = getattr(self.print_test_module, "print_secret_codes", self.dummy_print)
            else:
                self.print_entry_form = self.dummy_print
                self.print_doctor_visit_form = self.dummy_print
                self.print_late_form = self.dummy_print
                self.print_guidance_form = self.dummy_print
                self.print_permission_form = self.dummy_print
                self.print_secret_codes = self.dummy_print
            
            # استيراد وحدات طباعة إضافية من print1.py و print2_test.py
            self.print1_module = self.import_module_by_name("print1")
            if self.print1_module:
                self.print_doctor_visit_form_new = getattr(self.print1_module, "print_doctor_visit_form", self.dummy_print)
                print("تم تحميل print1.py بنجاح")
            else:
                self.print_doctor_visit_form_new = self.dummy_print
                print("فشل في تحميل print1.py")
            
            self.print2_test_module = self.import_module_by_name("print2_test")
            if self.print2_test_module:
                self.print_doctor_visit_form_alt = getattr(self.print2_test_module, "print_doctor_visit_form", self.dummy_print)
                self.print_school_certificates_list = getattr(self.print2_test_module, "print_school_certificates_list", self.dummy_print)
                self.print_activity_list = getattr(self.print2_test_module, "print_activity_list", self.dummy_print)
                print("تم تحميل print2_test.py بنجاح")
            else:
                self.print_doctor_visit_form_alt = self.dummy_print
                self.print_school_certificates_list = self.dummy_print
                self.print_activity_list = self.dummy_print
                print("فشل في تحميل print2_test.py")
                
        except Exception as e:
            print(f"خطأ في استيراد وحدات الطباعة: {str(e)}")
            # تعيين قيم افتراضية في حالة الخطأ
            self.print_entry_form_direct = self.dummy_print
            self.print_late_form_direct = self.dummy_print
            self.print_entry_form = self.dummy_print
            self.print_doctor_visit_form = self.dummy_print
            self.print_late_form = self.dummy_print
            self.print_guidance_form = self.dummy_print
            self.print_permission_form = self.dummy_print
            self.print_secret_codes = self.dummy_print
    
    def import_other_modules(self):
        """استيراد الوحدات الأخرى المطلوبة"""
        try:
            # استيراد وحدة بطاقة التلميذ
            self.sub10_module = self.import_module_by_name("sub10_window")
            if self.sub10_module:
                self.StudentCardWindow = getattr(self.sub10_module, "StudentCardWindow", None)
            else:
                self.StudentCardWindow = None
            
            # استيراد وحدة المساعدة
            self.help_guide_module = self.import_module_by_name("help_guide")
            
        except Exception as e:
            print(f"خطأ في استيراد الوحدات الأخرى: {str(e)}")
    
    def import_module_by_name(self, module_name):
        """استيراد وحدة برمجية من نفس المجلد"""
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            module_path = os.path.join(script_dir, f"{module_name}.py")
            
            if os.path.exists(module_path):
                spec = importlib.util.spec_from_file_location(module_name, module_path)
                if spec:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    return module
            return None
        except Exception as e:
            print(f"خطأ في استيراد وحدة {module_name}: {e}")
            return None
    
    def dummy_print(self, *args, **kwargs):
        """دالة طباعة وهمية في حالة عدم توفر وحدات الطباعة"""
        print("دالة الطباعة غير متوفرة")
        return False
    
    def get_selected_students_data(self):
        """الحصول على بيانات التلاميذ المحددين من الواجهة الحديثة"""
        try:
            # إذا كان لدينا selected_students محدث من النافذة الأصلية
            if hasattr(self, 'selected_students') and self.selected_students:
                return self.selected_students
            
            # يمكن تنفيذ طرق أخرى للحصول على البيانات حسب الحاجة
            return []
        except Exception as e:
            print(f"خطأ في الحصول على بيانات التلاميذ المحددين: {str(e)}")
            return []
    
    def show_message(self, title, message, icon_type="info"):
        """عرض رسالة للمستخدم"""
        try:
            # إرسال الرسالة إلى النافذة الأصلية
            if hasattr(self.parent, 'emit_log'):
                self.parent.emit_log(f"{title}: {message}", icon_type)
            else:
                print(f"{title}: {message}")
                
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {str(e)}")
    
    # ===== وظائف الأزرار =====
    
    def add_to_entry_sheet(self):
        """وظيفة زر ورقة الدخول - محسنة"""
        try:
            print("=== بدء وظيفة ورقة الدخول ===")
            
            selected_students = self.get_selected_students_data()
            print(f"عدد التلاميذ المحددين: {len(selected_students)}")
            
            if not selected_students:
                print("❌ لا يوجد تلاميذ محددين")
                self.show_message("تنبيه", "الرجاء تحديد تلميذ واحد على الأقل", "warning")
                return {"success": False, "message": "لا يوجد تلاميذ محددين"}
            
            # طباعة أسماء التلاميذ المحددين
            for i, student in enumerate(selected_students):
                print(f"التلميذ {i+1}: {student.get('name', 'غير محدد')} - رمز: {student.get('code', 'غير محدد')}")
            
            # التحقق من وجود القسم
            section = getattr(self.parent, 'selected_section', None) or getattr(self.parent, 'current_section', '') or getattr(self.parent, 'selected_class', '')
            print(f"القسم المحدد: '{section}'")
            
            if not section:
                print("❌ لم يتم تحديد قسم")
                self.show_message("تنبيه", "الرجاء تحديد قسم أولاً", "warning")
                return {"success": False, "message": "لم يتم تحديد قسم"}
            
            # تحديد التاريخ والوقت
            current_date = self.current_date.toString("yyyy-MM-dd") if hasattr(self, 'current_date') else datetime.datetime.now().strftime("%Y-%m-%d")
            current_time = self.current_time.toString("hh:mm") if hasattr(self, 'current_time') else datetime.datetime.now().strftime("%H:%M")
            
            print(f"التاريخ: {current_date}, الوقت: {current_time}")
            
            # الحصول على السنة الدراسية والأسدس
            year, semester = self.get_current_school_year_and_semester()
            print(f"السنة الدراسية: {year}, الأسدس: {semester}")
            
            # التحقق من اتصال قاعدة البيانات
            if not self.db or not self.db.isOpen():
                print("❌ قاعدة البيانات غير متصلة")
                self.show_message("خطأ", "قاعدة البيانات غير متصلة", "error")
                return {"success": False, "message": "قاعدة البيانات غير متصلة"}
            
            print("✅ قاعدة البيانات متصلة")
            
            # معالجة إضافة التلاميذ إلى ورقة الدخول
            success_count = 0
            failed_count = 0
            processed_students = []
            
            # بدء المعاملة
            print("بدء معاملة قاعدة البيانات...")
            self.db.transaction()
            
            try:
                for i, student in enumerate(selected_students):
                    print(f"\n--- معالجة التلميذ {i+1}: {student.get('name', 'غير محدد')} ---")
                    
                    try:
                        student_code = student.get('code', '')
                        student_name = student.get('name', '')
                        
                        if not student_code:
                            print(f"❌ رمز التلميذ فارغ")
                            failed_count += 1
                            continue
                        
                        print(f"رمز التلميذ: {student_code}")
                        print(f"اسم التلميذ: {student_name}")
                        
                        # إدراج بيانات في جدول ورقة_السماح_بالدخول
                        insert_query = QSqlQuery(db=self.db)
                        insert_query.prepare("""
                            INSERT INTO ورقة_السماح_بالدخول(
                                السنة_الدراسية,
                                الأسدس,
                                الرقم_الترتيبي,
                                التاريخ,
                                الاسم_والنسب,
                                سماح,
                                الرمز,
                                الوقت,
                                ورقة_السماح
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """)
                        
                        # الحصول على الرقم الترتيبي للتلميذ
                        rt_value = student.get('rt', '0')
                        if not rt_value or rt_value == '':
                            rt_value = '0'
                        
                        insert_query.addBindValue(year)           # السنة_الدراسية
                        insert_query.addBindValue(semester)      # الأسدس
                        insert_query.addBindValue(rt_value)      # الرقم_الترتيبي
                        insert_query.addBindValue(current_date)  # التاريخ
                        insert_query.addBindValue(student_name)  # الاسم_والنسب
                        insert_query.addBindValue(1)            # سماح = 1
                        insert_query.addBindValue(student_code)  # الرمز
                        insert_query.addBindValue(current_time)  # الوقت
                        insert_query.addBindValue(' سماح ')     # ورقة_السماح
                        
                        print(f"📋 المعاملات المرسلة:")
                        print(f"   السنة_الدراسية: {year}")
                        print(f"   الأسدس: {semester}")
                        print(f"   الرقم_الترتيبي: {rt_value}")
                        print(f"   التاريخ: {current_date}")
                        print(f"   الاسم_والنسب: {student_name}")
                        print(f"   سماح: 1")
                        print(f"   الرمز: {student_code}")
                        print(f"   الوقت: {current_time}")
                        print(f"   ورقة_السماح: سماح")
                        
                        if insert_query.exec_():
                            print(f"✅ تم إدراج بيانات التلميذ في جدول ورقة_السماح_بالدخول")
                            
                            # تحديث إحصائيات السماح في جدول السجل_العام
                            update_query = QSqlQuery(db=self.db)
                            update_query.prepare("""
                                UPDATE السجل_العام
                                SET السماح = (
                                    SELECT COUNT(*)
                                    FROM ورقة_السماح_بالدخول w
                                    WHERE w.الرمز = ? AND w.سماح = 1
                                    AND w.السنة_الدراسية = ? AND w.الأسدس = ?
                                )
                                WHERE الرمز = ?
                            """)
                            update_query.addBindValue(student_code)
                            update_query.addBindValue(year)
                            update_query.addBindValue(semester)
                            update_query.addBindValue(student_code)
                            
                            if update_query.exec_():
                                print(f"✅ تم تحديث إحصائيات السماح في جدول السجل_العام")
                                success_count += 1
                                processed_students.append(student)
                                
                                # محاولة الطباعة
                                print(f"محاولة طباعة ورقة الدخول...")
                                print_result = self.try_print_entry_form(student, section, current_date, current_time)
                                print(f"نتيجة الطباعة: {print_result}")
                            else:
                                failed_count += 1
                                error_msg = update_query.lastError().text()
                                print(f"❌ خطأ في تحديث إحصائيات السماح للتلميذ {student_name}: {error_msg}")
                        else:
                            failed_count += 1
                            error_msg = insert_query.lastError().text()
                            print(f"❌ خطأ في إدراج بيانات التلميذ {student_name}: {error_msg}")
                            
                    except Exception as e:
                        print(f"❌ خطأ في معالجة التلميذ {student.get('name', 'غير محدد')}: {str(e)}")
                        failed_count += 1
                
                # تأكيد المعاملة إذا نجحت
                if success_count > 0:
                    self.db.commit()
                    print(f"✅ تم تأكيد المعاملة - نجح {success_count} تلميذ")
                    
                    # عرض رسالة النجاح
                    message = f"تم تسجيل السماح بالدخول لـ {success_count} تلميذ بنجاح"
                    if failed_count > 0:
                        message += f"\nفشل في إضافة {failed_count} تلميذ"
                    
                    print(f"رسالة النجاح: {message}")
                    self.show_message("نجح", message, "success")
                    
                    # إرجاع بيانات النجاح
                    return {
                        "success": True, 
                        "message": message,
                        "processed_count": success_count,
                        "failed_count": failed_count,
                        "processed_students": processed_students
                    }
                else:
                    self.db.rollback()
                    print(f"❌ تم إلغاء المعاملة - فشل جميع التلاميذ")
                    self.show_message("خطأ", "فشل في إضافة جميع التلاميذ إلى ورقة الدخول", "error")
                    return {"success": False, "message": "فشل في إضافة التلاميذ"}
                    
            except Exception as e:
                self.db.rollback()
                print(f"❌ خطأ في المعاملة، تم الإلغاء: {str(e)}")
                raise e
                
        except Exception as e:
            error_msg = f"خطأ في وظيفة ورقة الدخول: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("خطأ", error_msg, "error")
            return {"success": False, "message": error_msg}
    
    def try_print_entry_form(self, student, section, date_str, time_str):
        """محاولة طباعة ورقة الدخول باستخدام النموذج الأصلي من print_test.py"""
        try:
            # تحضير بيانات الطباعة
            students_list = [student]
            
            print(f"محاولة طباعة ورقة الدخول للتلميذ: {student.get('name', 'غير محدد')}")
            
            # استخدام النموذج الأصلي من print_test.py أولاً
            if hasattr(self, 'print_entry_form') and callable(self.print_entry_form):
                try:
                    print("استخدام النموذج الأصلي من print_test.py...")
                    result = self.print_entry_form(students_list, section, date_str, time_str)
                    if result:
                        print(f"تمت طباعة ورقة الدخول (النموذج الأصلي) للتلميذ: {student.get('name', '')}")
                        return True
                    else:
                        print("فشلت طباعة ورقة الدخول من النموذج الأصلي - النتيجة غير ناجحة")
                except Exception as e:
                    print(f"فشلت طباعة ورقة الدخول من النموذج الأصلي: {str(e)}")
            else:
                print("دالة طباعة ورقة الدخول الأصلية غير متوفرة")
            
            # محاولة الطباعة الحرارية المباشرة
            if hasattr(self, 'print_entry_form_direct') and callable(self.print_entry_form_direct):
                try:
                    print("محاولة الطباعة الحرارية المباشرة...")
                    result = self.print_entry_form_direct(students_list, section, date_str, time_str)
                    if result:
                        print(f"تمت طباعة ورقة الدخول حرارياً للتلميذ: {student.get('name', '')}")
                        return True
                    else:
                        print("فشلت الطباعة الحرارية - النتيجة غير ناجحة")
                except Exception as e:
                    print(f"فشلت الطباعة الحرارية: {str(e)}")
            else:
                print("دالة الطباعة الحرارية غير متوفرة")
            
            # محاولة الطباعة التقليدية كاحتياط أخير
            if hasattr(self, 'print_entry_form') and callable(self.print_entry_form):
                try:
                    print("محاولة الطباعة التقليدية...")
                    result = self.print_entry_form(students_list, section, date_str, time_str)
                    if result:
                        print(f"تمت طباعة ورقة الدخول تقليدياً للتلميذ: {student.get('name', '')}")
                        return True
                    else:
                        print("فشلت الطباعة التقليدية - النتيجة غير ناجحة")
                except Exception as e:
                    print(f"فشلت الطباعة التقليدية: {str(e)}")
            else:
                print("دالة الطباعة التقليدية غير متوفرة")
            
            print(f"لم تتم طباعة ورقة الدخول للتلميذ: {student.get('name', '')} - جميع وحدات الطباعة فشلت أو غير متوفرة")
            
            # إنشاء إشعار بديل إذا فشلت جميع أنواع الطباعة
            self.create_entry_notification(student, section, date_str, time_str)
            return True  # إرجاع True لأن الإشعار تم إنشاؤه
            
        except Exception as e:
            print(f"خطأ عام في محاولة طباعة ورقة الدخول: {str(e)}")
            return False
    
    def create_entry_notification(self, student, section, date_str, time_str):
        """إنشاء إشعار بديل عند فشل الطباعة"""
        try:
            import os
            import datetime
            
            # إنشاء مجلد للإشعارات
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            notifications_dir = os.path.join(desktop_path, "إشعارات ورقة الدخول")
            os.makedirs(notifications_dir, exist_ok=True)
            
            # إنشاء اسم الملف
            student_name = student.get('name', 'بدون_اسم').replace(' ', '_')
            student_code = student.get('code', 'بدون_رمز')
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            
            file_name = f"إشعار_دخول_{student_name}_{student_code}_{timestamp}.txt"
            file_path = os.path.join(notifications_dir, file_name)
            
            # إنشاء محتوى الإشعار
            content = f"""
=== إشعار السماح بالدخول ===

اسم التلميذ: {student.get('name', 'غير محدد')}
رمز التلميذ: {student.get('code', 'غير محدد')}
القسم: {section}
التاريخ: {date_str}
الوقت: {time_str}

تم تسجيل السماح بالدخول بنجاح في قاعدة البيانات.
ملاحظة: لم تتم الطباعة الفعلية بسبب عدم توفر أو فشل وحدات الطباعة.

تاريخ الإنشاء: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # كتابة الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"تم إنشاء إشعار بديل في: {file_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء الإشعار البديل: {str(e)}")
            return False
    
    def get_current_school_year_and_semester(self):
        """الحصول على السنة الدراسية والأسدس الحاليين"""
        try:
            if self.current_academic_year:
                year = self.current_academic_year
            else:
                year = "2024-2025"  # قيمة افتراضية
            
            # الحصول على الأسدس من قاعدة البيانات
            semester = "الأول"  # قيمة افتراضية
            if self.db and self.db.isOpen():
                query = QSqlQuery(db=self.db)
                if query.exec_("SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1"):
                    if query.next():
                        semester = query.value(0) or semester
            
            return year, semester
            
        except Exception as e:
            print(f"خطأ في الحصول على السنة الدراسية والأسدس: {str(e)}")
            return "2024-2025", "الأول"
    
    def add_to_late_sheet(self):
        """وظيفة زر ورقة التأخر"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل", "warning")
                return
            
            # معالجة إضافة التلاميذ إلى ورقة التأخر
            success_count = 0
            failed_count = 0
            
            for student in selected_students:
                try:
                    # إضافة التلميذ إلى ورقة السماح بالدخول كتأخر
                    query = QSqlQuery(db=self.db)
                    query.prepare("""
                        INSERT INTO ورقة_السماح_بالدخول 
                        (الرمز, ورقة_السماح, التاريخ, الوقت, السنة_الدراسية, الأسدس)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """)
                    query.addBindValue(student.get('code', ''))
                    query.addBindValue(' تأخر ')
                    query.addBindValue(self.parent.current_date.toString("yyyy-MM-dd"))
                    query.addBindValue(self.parent.current_time.toString("hh:mm:ss"))
                    query.addBindValue(self.current_academic_year)
                    query.addBindValue("الأول")  # يمكن تحديثها حسب الحاجة
                    
                    if query.exec_():
                        success_count += 1
                        
                        # طباعة ورقة التأخر إذا كانت الطباعة متوفرة
                        try:
                            # استخدام الطباعة الحرارية إذا كانت متوفرة
                            if hasattr(self, 'print_late_form_direct') and callable(self.print_late_form_direct):
                                self.print_late_form_direct([student])
                            else:
                                print("دالة طباعة ورقة التأخر غير متوفرة")
                        except Exception as print_error:
                            print(f"تحذير: خطأ في طباعة ورقة التأخر: {str(print_error)}")
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"خطأ في إضافة التلميذ {student.get('name', 'غير محدد')}: {str(e)}")
                    failed_count += 1
            
            # عرض رسالة النتيجة
            if success_count > 0:
                message = f"تم إضافة {success_count} تلميذ إلى ورقة التأخر بنجاح"
                if failed_count > 0:
                    message += f"\nفشل في إضافة {failed_count} تلميذ"
                self.show_message("نجح", message, "success")
            else:
                self.show_message("خطأ", "فشل في إضافة البيانات إلى ورقة التأخر", "error")
                
        except Exception as e:
            print(f"خطأ في وظيفة ورقة التأخر: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def print_student_card(self):
        """وظيفة زر ورقة التوجيه (بطاقة التلميذ)"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد للطباعة", "warning")
                return
            
            if len(selected_students) > 1:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد فقط للطباعة", "warning")
                return
            
            student = selected_students[0]
            
            # فتح نافذة بطاقة التلميذ
            if self.StudentCardWindow:
                try:
                    # تمرير None بدلاً من self.parent لتجنب خطأ النوع
                    card_window = self.StudentCardWindow(parent=None)
                    if hasattr(card_window, 'set_student_data'):
                        card_window.set_student_data(student)
                    card_window.show()
                    self.show_message("نجح", f"تم فتح بطاقة التلميذ: {student.get('name', 'غير محدد')}", "success")
                except Exception as e:
                    print(f"خطأ في فتح نافذة بطاقة التلميذ: {str(e)}")
                    # محاولة بديلة: طباعة معلومات التلميذ
                    self.show_message("معلومات", f"بيانات التلميذ: {student.get('name', 'غير محدد')} - {student.get('code', 'غير محدد')}", "info")
            else:
                self.show_message("معلومات", "وحدة بطاقة التلميذ غير متوفرة", "info")
                # عرض بيانات التلميذ كبديل
                student_info = f"الاسم: {student.get('name', 'غير محدد')}\nالرمز: {student.get('code', 'غير محدد')}\nالترتيب: {student.get('rt', 'غير محدد')}"
                self.show_message("بيانات التلميذ", student_info, "info")
                
        except Exception as e:
            print(f"خطأ في وظيفة ورقة التوجيه: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def show_violations_permission_slip(self):
        """وظيفة زر ورقة الاستئذان"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل", "warning")
                return
            
            # طباعة ورقة الاستئذان للتلاميذ المحددين
            success_count = 0
            failed_count = 0
            
            for student in selected_students:
                try:
                    if self.print_permission_form:
                        if self.print_permission_form(student):
                            success_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"خطأ في طباعة ورقة الاستئذان للتلميذ {student.get('name', 'غير محدد')}: {str(e)}")
                    failed_count += 1
            
            # عرض رسالة النتيجة
            if success_count > 0:
                message = f"تم طباعة ورقة الاستئذان لـ {success_count} تلميذ بنجاح"
                if failed_count > 0:
                    message += f"\nفشل في طباعة {failed_count} ورقة"
                self.show_message("نجح", message, "success")
            else:
                self.show_message("خطأ", "فشل في طباعة ورقة الاستئذان", "error")
                
        except Exception as e:
            print(f"خطأ في وظيفة ورقة الاستئذان: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def print_secret_code(self):
        """وظيفة زر الرمز السري"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل", "warning")
                return
            
            # طباعة الرمز السري للتلاميذ المحددين
            success_count = 0
            failed_count = 0
            
            for student in selected_students:
                try:
                    if self.print_secret_codes:
                        if self.print_secret_codes(student):
                            success_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"خطأ في طباعة الرمز السري للتلميذ {student.get('name', 'غير محدد')}: {str(e)}")
                    failed_count += 1
            
            # عرض رسالة النتيجة
            if success_count > 0:
                message = f"تم طباعة الرمز السري لـ {success_count} تلميذ بنجاح"
                if failed_count > 0:
                    message += f"\nفشل في طباعة {failed_count} رمز"
                self.show_message("نجح", message, "success")
            else:
                self.show_message("خطأ", "فشل في طباعة الرمز السري", "error")
                
        except Exception as e:
            print(f"خطأ في وظيفة الرمز السري: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def open_absence_justification(self):
        """وظيفة زر مسك الطلبات"""
        try:
            # فتح نافذة مسك الطلبات (يجب تطوير هذه النافذة)
            self.show_message("معلومات", "سيتم تطوير نافذة مسك الطلبات قريباً", "info")
            
        except Exception as e:
            print(f"خطأ في وظيفة مسك الطلبات: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def show_help(self):
        """وظيفة زر التعليمات"""
        try:
            # عرض نافذة المساعدة
            if self.help_guide_module and hasattr(self.help_guide_module, "HelpGuide"):
                help_window = self.help_guide_module.HelpGuide(parent=self.parent)
                help_window.show()
            else:
                self.show_message("معلومات", "نافذة المساعدة غير متوفرة حالياً", "info")
                
        except Exception as e:
            print(f"خطأ في وظيفة التعليمات: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def print_doctor_visit(self):
        """وظيفة زر زيارة الطبيب"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل", "warning")
                return
            
            # طباعة نموذج زيارة الطبيب
            success_count = 0
            failed_count = 0
            
            for student in selected_students:
                try:
                    if self.print_doctor_visit_form:
                        if self.print_doctor_visit_form(student):
                            success_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    print(f"خطأ في طباعة نموذج زيارة الطبيب للتلميذ {student.get('name', 'غير محدد')}: {str(e)}")
                    failed_count += 1
            
            # عرض رسالة النتيجة
            if success_count > 0:
                message = f"تم طباعة نموذج زيارة الطبيب لـ {success_count} تلميذ بنجاح"
                if failed_count > 0:
                    message += f"\nفشل في طباعة {failed_count} نموذج"
                self.show_message("نجح", message, "success")
            else:
                self.show_message("خطأ", "فشل في طباعة نموذج زيارة الطبيب", "error")
                
        except Exception as e:
            print(f"خطأ في وظيفة زيارة الطبيب: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def export_selected_students(self):
        """وظيفة زر تصدير"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل للتصدير", "warning")
                return
            
            # فتح نافذة تصدير البيانات (يجب تطوير هذه النافذة)
            self.show_message("معلومات", f"سيتم تصدير بيانات {len(selected_students)} تلميذ", "info")
            
        except Exception as e:
            print(f"خطأ في وظيفة التصدير: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def show_regulations_card(self):
        """وظيفة زر بطاقة اللوائح"""
        try:
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد لعرض بطاقة اللوائح", "warning")
                return
            
            if len(selected_students) > 1:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد فقط لعرض بطاقة اللوائح", "warning")
                return
            
            student = selected_students[0]
            
            # عرض بطاقة اللوائح للتلميذ المحدد
            self.show_message("معلومات", f"عرض بطاقة اللوائح للتلميذ: {student.get('name', 'غير محدد')}", "info")
            
        except Exception as e:
            print(f"خطأ في وظيفة بطاقة اللوائح: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def update_data(self):
        """وظيفة زر تحديث البيانات"""
        try:
            # الحصول على السنة الدراسية والأسدس الحاليين
            school_year_query = QSqlQuery(db=self.db)
            school_year_query.exec_("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            current_year = ""
            current_semester = ""
            if school_year_query.next():
                current_year = school_year_query.value(0)
                current_semester = school_year_query.value(1)
            
            # تحديث عمود السماح
            update_entry_query = QSqlQuery(db=self.db)
            update_entry_query.prepare("""
                UPDATE السجل_العام
                SET السماح = (
                    SELECT COUNT(*)
                    FROM ورقة_السماح_بالدخول
                    WHERE ورقة_السماح_بالدخول.الرمز = السجل_العام.الرمز
                    AND ورقة_السماح_بالدخول.ورقة_السماح = ' سماح '
                    AND ورقة_السماح_بالدخول.السنة_الدراسية = ?
                    AND ورقة_السماح_بالدخول.الأسدس = ?
                )
            """)
            update_entry_query.addBindValue(current_year)
            update_entry_query.addBindValue(current_semester)
            
            # تحديث عمود التأخر
            update_late_query = QSqlQuery(db=self.db)
            update_late_query.prepare("""
                UPDATE السجل_العام
                SET التأخر = (
                    SELECT COUNT(*)
                    FROM ورقة_السماح_بالدخول
                    WHERE ورقة_السماح_بالدخول.الرمز = السجل_العام.الرمز
                    AND ورقة_السماح_بالدخول.ورقة_السماح = ' تأخر '
                    AND ورقة_السماح_بالدخول.السنة_الدراسية = ?
                    AND ورقة_السماح_بالدخول.الأسدس = ?
                )
            """)
            update_late_query.addBindValue(current_year)
            update_late_query.addBindValue(current_semester)
            
            # تحديث عمود عدد المخالفات
            update_violations_query = QSqlQuery(db=self.db)
            update_violations_query.prepare("""
                UPDATE السجل_العام
                SET عدد_المخالفات = (
                    SELECT COUNT(*)
                    FROM المخالفات
                    WHERE المخالفات.رمز_التلميذ = السجل_العام.الرمز
                    AND المخالفات.السنة_الدراسية = ?
                    AND المخالفات.الأسدس = ?
                )
            """)
            update_violations_query.addBindValue(current_year)
            update_violations_query.addBindValue(current_semester)
            
            # بدء المعاملة
            self.db.transaction()
            
            try:
                # تنفيذ استعلامات التحديث
                entry_success = update_entry_query.exec_()
                late_success = update_late_query.exec_()
                violations_success = update_violations_query.exec_()
                
                if entry_success and late_success and violations_success:
                    self.db.commit()
                    self.show_message("نجح", "تم تحديث البيانات بنجاح", "success")
                    
                    # إعادة تحميل البيانات في الواجهة
                    if hasattr(self.parent, 'reload_data'):
                        self.parent.reload_data()
                else:
                    self.db.rollback()
                    self.show_message("خطأ", "فشل في تحديث البيانات", "error")
            except Exception as e:
                self.db.rollback()
                raise e
                
        except Exception as e:
            print(f"خطأ في وظيفة تحديث البيانات: {str(e)}")
            self.show_message("خطأ", f"حدث خطأ في تحديث البيانات: {str(e)}", "error")
    
    def add_to_late_sheet_new(self):
        """وظيفة زر ورقة التأخر المحسنة - نسخة محدثة مع قاعدة بيانات محسنة وطباعة صامتة"""
        try:
            print("=== بدء وظيفة ورقة التأخر ===")
            
            # التحقق من وجود تلاميذ محددين
            selected_students = self.get_selected_students_data()
            if not selected_students:
                self.show_message("تنبيه", "يرجى تحديد تلميذ واحد على الأقل", "warning")
                return {"success": False, "message": "لا يوجد تلاميذ محددين"}
            
            print(f"عدد التلاميذ المحددين: {len(selected_students)}")
            for i, student in enumerate(selected_students, 1):
                print(f"التلميذ {i}: {student.get('name', 'غير محدد')} - رمز: {student.get('code', 'غير محدد')}")
            
            # التحقق من وجود قسم محدد
            if not self.parent.selected_class:
                self.show_message("تنبيه", "يرجى تحديد القسم أولاً", "warning")
                return {"success": False, "message": "لا يوجد قسم محدد"}
            
            section = self.parent.selected_class
            print(f"القسم المحدد: '{section}'")
            
            # الحصول على التاريخ والوقت
            if self.parent.use_custom_datetime:
                current_date = self.parent.current_date.toString("yyyy-MM-dd")
                current_time = self.parent.current_time.toString("hh:mm")
            else:
                current_date = QDate.currentDate().toString("yyyy-MM-dd")
                current_time = QTime.currentTime().toString("hh:mm")
            
            print(f"التاريخ: {current_date}, الوقت: {current_time}")
            
            # الحصول على السنة الدراسية والأسدس
            year, semester = self.get_current_school_year_and_semester()
            print(f"السنة الدراسية: {year}, الأسدس: {semester}")
            
            # التحقق من اتصال قاعدة البيانات
            if not self.db or not self.db.isOpen():
                self.show_message("خطأ", "قاعدة البيانات غير متصلة", "error")
                return {"success": False, "message": "قاعدة البيانات غير متصلة"}
            
            print("✅ قاعدة البيانات متصلة")
            print("بدء معاملة قاعدة البيانات...")
            
            # بدء معاملة قاعدة البيانات
            self.db.transaction()
            
            success_count = 0
            failed_count = 0
            error_messages = []
            
            try:
                # معالجة كل تلميذ
                for i, student in enumerate(selected_students, 1):
                    try:
                        print(f"\n--- معالجة التلميذ {i}: {student.get('name', 'غير محدد')} ---")
                        
                        student_code = student.get('code', '')
                        student_name = student.get('name', '')
                        student_rt = student.get('rt', i)
                        
                        print(f"رمز التلميذ: {student_code}")
                        print(f"اسم التلميذ: {student_name}")
                        
                        # إدراج البيانات في جدول ورقة_السماح_بالدخول
                        late_query = QSqlQuery(self.db)
                        late_query.prepare("""
                            INSERT INTO ورقة_السماح_بالدخول 
                            (السنة_الدراسية, الأسدس, الرقم_الترتيبي, التاريخ, الاسم_والنسب, 
                             تأخر, الرمز, الوقت, ورقة_السماح)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """)
                        
                        late_query.addBindValue(year)
                        late_query.addBindValue(semester)
                        late_query.addBindValue(student_rt)
                        late_query.addBindValue(current_date)
                        late_query.addBindValue(student_name)
                        late_query.addBindValue(1)  # تأخر = 1
                        late_query.addBindValue(student_code)
                        late_query.addBindValue(current_time)
                        late_query.addBindValue("تأخر")
                        
                        print("📋 المعاملات المرسلة:")
                        print(f"   السنة_الدراسية: {year}")
                        print(f"   الأسدس: {semester}")
                        print(f"   الرقم_الترتيبي: {student_rt}")
                        print(f"   التاريخ: {current_date}")
                        print(f"   الاسم_والنسب: {student_name}")
                        print(f"   تأخر: 1")
                        print(f"   الرمز: {student_code}")
                        print(f"   الوقت: {current_time}")
                        print(f"   ورقة_السماح: تأخر")
                        
                        if late_query.exec_():
                            print("✅ تم إدراج بيانات التلميذ في جدول ورقة_السماح_بالدخول")
                            
                            # تحديث إحصائيات التأخر في جدول السجل_العام
                            stats_query = QSqlQuery(self.db)
                            stats_query.prepare("""
                                UPDATE السجل_العام 
                                SET تأخر = COALESCE(تأخر, 0) + 1
                                WHERE الرمز = ? AND السنة_الدراسية = ?
                            """)
                            stats_query.addBindValue(student_code)
                            stats_query.addBindValue(year)
                            
                            if stats_query.exec_():
                                print("✅ تم تحديث إحصائيات التأخر في جدول السجل_العام")
                            else:
                                print(f"⚠️ تحذير: لم يتم تحديث إحصائيات التأخر: {stats_query.lastError().text()}")
                            
                            # محاولة طباعة ورقة التأخر
                            print("محاولة طباعة ورقة التأخر...")
                            try:
                                print_result = self.try_print_late_form(student, section, current_date, current_time)
                                print(f"نتيجة الطباعة: {print_result}")
                            except Exception as print_error:
                                print(f"تحذير: خطأ في طباعة ورقة التأخر: {str(print_error)}")
                            
                            success_count += 1
                            
                        else:
                            error_msg = late_query.lastError().text()
                            print(f"❌ فشل في إدراج بيانات التلميذ: {error_msg}")
                            error_messages.append(f"التلميذ {student_name}: {error_msg}")
                            failed_count += 1
                        
                    except Exception as student_error:
                        error_msg = f"خطأ في معالجة التلميذ {student.get('name', 'غير محدد')}: {str(student_error)}"
                        print(f"❌ {error_msg}")
                        error_messages.append(error_msg)
                        failed_count += 1
                
                # إنهاء المعاملة
                if success_count > 0 and failed_count == 0:
                    if self.db.commit():
                        print(f"✅ تم تأكيد المعاملة - نجح {success_count} تلميذ")
                        success_message = f"تم تسجيل التأخر لـ {success_count} تلميذ بنجاح"
                        print(f"رسالة النجاح: {success_message}")
                        self.show_message("نجاح العملية", success_message, "success")
                        print(f"✅ نجحت عملية ورقة التأخر لـ {success_count} طالب")
                        return {"success": True, "message": success_message}
                    else:
                        self.db.rollback()
                        error_msg = "فشل في تأكيد المعاملة"
                        print(f"❌ {error_msg}")
                        self.show_message("خطأ", error_msg, "error")
                        return {"success": False, "message": error_msg}
                else:
                    self.db.rollback()
                    if len(error_messages) > 0:
                        error_msg = f"فشل في معالجة بعض التلاميذ:\n" + "\n".join(error_messages)
                    else:
                        error_msg = "لم يتم معالجة أي تلميذ بنجاح"
                    print(f"❌ {error_msg}")
                    self.show_message("خطأ", error_msg, "error")
                    return {"success": False, "message": error_msg}
                
            except Exception as transaction_error:
                self.db.rollback()
                print(f"❌ خطأ في المعاملة، تم الإلغاء: {str(transaction_error)}")
                error_msg = f"خطأ في معاملة قاعدة البيانات: {str(transaction_error)}"
                self.show_message("خطأ", error_msg, "error")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            error_msg = f"خطأ في وظيفة ورقة التأخر: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("خطأ", error_msg, "error")
            return {"success": False, "message": error_msg}
    
    def try_print_late_form(self, student, section, date_str, time_str):
        """محاولة طباعة ورقة التأخر باستخدام النموذج الأصلي من print_test.py"""
        try:
            # تحضير بيانات الطباعة
            students_list = [student]
            
            print(f"محاولة طباعة ورقة التأخر للتلميذ: {student.get('name', 'غير محدد')}")
            
            # استخدام النموذج الأصلي من print_test.py أولاً
            if hasattr(self, 'print_late_form') and callable(self.print_late_form):
                try:
                    print("استخدام النموذج الأصلي من print_test.py...")
                    result = self.print_late_form(students_list, section, date_str, time_str)
                    if result:
                        print(f"تمت طباعة ورقة التأخر (النموذج الأصلي) للتلميذ: {student.get('name', '')}")
                        return True
                    else:
                        print("فشلت طباعة ورقة التأخر من النموذج الأصلي - النتيجة غير ناجحة")
                except Exception as e:
                    print(f"فشلت طباعة ورقة التأخر من النموذج الأصلي: {str(e)}")
            else:
                print("دالة طباعة ورقة التأخر الأصلية غير متوفرة")
            
            # محاولة الطباعة الحرارية المباشرة
            if hasattr(self, 'print_late_form_direct') and callable(self.print_late_form_direct):
                try:
                    print("محاولة الطباعة الحرارية المباشرة...")
                    result = self.print_late_form_direct(students_list, section, date_str, time_str)
                    if result:
                        print(f"تمت طباعة ورقة التأخر حرارياً للتلميذ: {student.get('name', '')}")
                        return True
                    else:
                        print("فشلت الطباعة الحرارية - النتيجة غير ناجحة")
                except Exception as e:
                    print(f"فشلت الطباعة الحرارية: {str(e)}")
            else:
                print("دالة الطباعة الحرارية غير متوفرة")
            
            # محاولة استخدام print2_test.py للطباعة الصامتة
            if hasattr(self, 'print2_test_module') and self.print2_test_module:
                try:
                    print("محاولة الطباعة باستخدام print2_test.py...")
                    print_late_form_alt = getattr(self.print2_test_module, "print_late_form", None)
                    if print_late_form_alt and callable(print_late_form_alt):
                        result = print_late_form_alt(
                            students_list, section, date_str, time_str, 
                            silent_print=True, use_regular_printer=True
                        )
                        if result:
                            print(f"تمت طباعة ورقة التأخر باستخدام print2_test.py للتلميذ: {student.get('name', '')}")
                            return True
                    else:
                        print("دالة print_late_form غير متوفرة في print2_test.py")
                except Exception as e:
                    print(f"فشلت الطباعة باستخدام print2_test.py: {str(e)}")
            
            print(f"لم تتم طباعة ورقة التأخر للتلميذ: {student.get('name', '')} - جميع وحدات الطباعة فشلت أو غير متوفرة")
            
            # إنشاء إشعار بديل إذا فشلت جميع أنواع الطباعة
            self.create_late_notification(student, section, date_str, time_str)
            return True  # إرجاع True لأن الإشعار تم إنشاؤه
            
        except Exception as e:
            print(f"خطأ عام في محاولة طباعة ورقة التأخر: {str(e)}")
            return False
    
    def create_late_notification(self, student, section, date_str, time_str):
        """إنشاء إشعار بديل عند فشل طباعة ورقة التأخر"""
        try:
            import os
            import datetime
            
            # إنشاء مجلد للإشعارات
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            notifications_dir = os.path.join(desktop_path, "إشعارات ورقة التأخر")
            os.makedirs(notifications_dir, exist_ok=True)
            
            # إنشاء اسم الملف
            student_name = student.get('name', 'بدون_اسم').replace(' ', '_')
            student_code = student.get('code', 'بدون_رمز')
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            
            file_name = f"إشعار_تأخر_{student_name}_{student_code}_{timestamp}.txt"
            file_path = os.path.join(notifications_dir, file_name)
            
            # إنشاء محتوى الإشعار
            content = f"""إشعار تأخر تلميذ
===============================

اسم التلميذ: {student.get('name', 'غير محدد')}
رمز التلميذ: {student.get('code', 'غير محدد')}
القسم: {section}
التاريخ: {date_str}
الوقت: {time_str}

تم تسجيل تأخر هذا التلميذ في النظام.

ملاحظة: تم إنشاء هذا الإشعار تلقائياً عند فشل عملية الطباعة المباشرة.
تاريخ الإنشاء: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # كتابة الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"تم إنشاء إشعار تأخر بديل في: {file_path}")
            
        except Exception as e:
            print(f"خطأ في إنشاء إشعار التأخر البديل: {str(e)}")

    # ...existing code...
