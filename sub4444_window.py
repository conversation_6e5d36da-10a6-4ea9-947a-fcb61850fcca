# sub4444_window.py
# نافذة الحراسة العامة الحديثة - منهجية Python + HTML
# تحويل كامل من sub4_window.py إلى واجهة حديثة باستخدام HTML/CSS/JavaScript

import sys
import os
import sqlite3
import datetime
import importlib.util
import importlib
import pandas as pd
import json
from PyQt5.QtCore import Qt, QDate, QTime, QModelIndex, pyqtSlot, QSize, QItemSelection, QTimer, pyqtSignal, QUrl
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QComboBox,
    QPushButton, QDateEdit, QTimeEdit, QTableView, QFrame,
    QGraphicsDropShadowEffect, QAbstractItemView, QHeaderView,
    QSizePolicy, QLabel, QStyledItemDelegate, QMessageBox, QCheckBox,
    QStyle, QStyleOptionButton, QDialog, QTextBrowser, QListWidget,
    QListWidgetItem, QLineEdit, QRadioButton, QButtonGroup, QFileDialog,
    QGroupBox, QGridLayout, QScrollArea, QTableWidget, QTableWidgetItem, QToolTip,
    QWidget
)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtGui import QPixmap, QFont, QIcon, QColor, QPalette
from PyQt5.QtSql import QSqlDatabase, QSqlQueryModel, QSqlQuery

def import_module_by_name(module_name):
    """استيراد وحدة برمجية من نفس المجلد الذي يتواجد فيه البرنامج الرئيسي"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    module_path = os.path.join(script_dir, f"{module_name}.py")

    if os.path.exists(module_path):
        try:
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"تم استيراد وحدة {module_name} بنجاح.")
                return module
        except Exception as e:
            print(f"خطأ في استيراد وحدة {module_name}: {e}")
            return None

    print(f"لم يتم العثور على وحدة {module_name}")
    return None

# استيراد وحدة رسائل التأكيد المخصصة
try:
    from sub100_window import ConfirmationDialogs
    CUSTOM_DIALOGS_IMPORTED = True
except ImportError:
    CUSTOM_DIALOGS_IMPORTED = False
    print("تعذر استيراد وحدة رسائل التأكيد المخصصة")

# استيراد واجهة البطائق المدرسية والمخالفات
try:
    sub10_module = import_module_by_name("sub10_window")
    if sub10_module:
        StudentCardWindow = getattr(sub10_module, "StudentCardWindow", None)
        HIRASA_IMPORTED = True
    else:
        StudentCardWindow = None
        HIRASA_IMPORTED = False
except Exception:
    StudentCardWindow = None
    HIRASA_IMPORTED = False
    print("خطأ في استيراد واجهة البطائق المدرسية أو المخالفات: No module named 'student_card_ui'")

# استيراد وحدات أخرى
module_mappings = {
    'sub11_window': 'نافذة مسك المخالفات',
    'sub14_window': 'نافذة معالجة تبرير الغياب',
    'sub13_window': 'نافذة تبرير الغياب',
    'sub16_window': 'نافذة توثيق زيارة أولياء الأمور',
    'sub17_window': 'نافذة معالجة توثيق زيارة أولياء الأمور',
    'sub12_window': 'نافذة معالجة المخالفات'
}

imported_modules = {}
for module_name, description in module_mappings.items():
    try:
        module = import_module_by_name(module_name)
        if module:
            imported_modules[module_name] = module
        else:
            print(f"لم يتم العثور على وحدة {module_name}")
            print(f"{description} غير متوفرة")
    except Exception as e:
        print(f"لم يتم العثور على وحدة {module_name}")
        print(f"{description} غير متوفرة")

# استيراد وحدات الطباعة
def dummy_print(*args, **kwargs):
    return False

print_test_module = import_module_by_name("print_test")
if print_test_module:
    print_entry_form = getattr(print_test_module, "print_entry_form", dummy_print)
    print_doctor_visit_form = getattr(print_test_module, "print_doctor_visit_form", dummy_print)
    print_late_form = getattr(print_test_module, "print_late_form", dummy_print)
    print_guidance_form = getattr(print_test_module, "print_guidance_form", dummy_print)
    print_permission_form = getattr(print_test_module, "print_permission_form", dummy_print)
    print_secret_codes = getattr(print_test_module, "print_secret_codes", dummy_print)
else:
    print_entry_form = print_doctor_visit_form = print_late_form = dummy_print
    print_guidance_form = print_permission_form = print_secret_codes = dummy_print

# استيراد وحدة الطباعة الحرارية
print_entry_form_direct = print_late_form_direct = None

try:
    thermal_image_print = import_module_by_name("thermal_image_print")
    if thermal_image_print:
        print_entry_form_direct = thermal_image_print.print_entry_form_direct
        print_late_form_direct = thermal_image_print.print_late_form_direct
        print("تم استيراد وحدة الطباعة الحرارية (تحويل النص إلى صورة) بنجاح")
except Exception as e:
    print(f"تحذير: لم يتم العثور على وحدة الطباعة الحرارية: {e}")

# استيراد طباعة print5_test.py للطابعة الحرارية
print5_test_module = import_module_by_name("print5_test")
if print5_test_module:
    print("تم تحميل print5_test.py بنجاح")
    print_certificates_handler = getattr(print5_test_module, "print_certificates_handler", dummy_print)
    print_activities_handler = getattr(print5_test_module, "print_activities_handler", dummy_print)
else:
    print("تحذير: ملف print5_test.py غير موجود أو يحتوي على أخطاء.")
    print_certificates_handler = dummy_print
    print_activities_handler = dummy_print

# استيراد الوحدات المساعدة
try:
    from sub44_window_html import GuardWindowFunctions
    print("تم تحميل ملف الوظائف المساعد بنجاح")
except ImportError as e:
    print(f"خطأ في استيراد ملف الوظائف المساعد: {e}")
    GuardWindowFunctions = None

class GuardWindow(QMainWindow):
    """نافذة الحراسة العامة الحديثة باستخدام HTML/CSS/JavaScript"""
    
    # إشارات للتواصل مع JavaScript
    studentsUpdated = pyqtSignal(str)
    dataUpdated = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # متغيرات النظام
        self.db = None
        self.current_academic_year = None
        self.current_date = QDate.currentDate()
        self.current_time = QTime.currentTime()
        self.use_custom_datetime = False
        self.selected_class = None
        self.levels_model = None
        self.classes_model = None
        self.lists_model = None
        self.all_selected_students = set()  # قائمة لتخزين رموز التلاميذ المحددين من جميع الأقسام
        self.active_student_window = None
        self.code_column_index = 1  # مؤشر عمود الرمز
        self.checkbox_column_index = 0  # مؤشر عمود مربع الاختيار
        
        # إعدادات الخطوط المستخدمة في الملف الأصلي
        self.font_calibri_13_bold = QFont('Calibri', 13)
        self.font_calibri_13_bold.setBold(True)
        self.font_calibri_13_normal = QFont('Calibri', 13)
        self.font_calibri_12_normal = QFont('Calibri', 12)
        
        # تهيئة اتصال قاعدة البيانات
        self.init_database()
        
        # تهيئة الوظائف المساعدة
        if GuardWindowFunctions:
            self.functions = GuardWindowFunctions(self)
        
        # إعداد النافذة
        self.init_ui()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def init_database(self):
        """تهيئة اتصال قاعدة البيانات"""
        try:
            db_path = os.path.join(os.path.dirname(__file__), "data.db")
            if os.path.exists(db_path):
                self.db = QSqlDatabase.addDatabase("QSQLITE", "guard_connection")
                self.db.setDatabaseName(db_path)
                if self.db.open():
                    print("✅ تم الاتصال بقاعدة البيانات بنجاح")
                    # الحصول على السنة الدراسية الحالية
                    query = QSqlQuery(db=self.db)
                    if query.exec_("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1") and query.next():
                        self.current_academic_year = query.value(0)
                        print(f"السنة الدراسية الحالية: {self.current_academic_year}")
                else:
                    print("❌ فشل في الاتصال بقاعدة البيانات")
            else:
                print("❌ ملف قاعدة البيانات غير موجود")
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("نافذة الحراسة العامة - الإصدار الحديث")
        self.setGeometry(100, 100, 1400, 900)
        
        # تحديد أيقونة النافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
        # إعداد قناة التواصل مع JavaScript
        self.channel = QWebChannel()
        self.channel.registerObject("pyguard", self)
        self.web_view.page().setWebChannel(self.channel)
        
        # تحميل محتوى HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)
        
        # ربط الإشارات
        self.studentsUpdated.connect(self.on_students_updated)
        self.dataUpdated.connect(self.on_data_updated)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحديث نماذج البيانات
            self.update_levels_model()
            # تحديث الأقسام للمستوى الأول إذا كان متوفراً
            if self.levels_model and self.levels_model.rowCount() > 0:
                first_level = self.levels_model.data(self.levels_model.index(0, 0))
                self.update_classes_model(first_level)
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")
    
    def update_levels_model(self, guard_filter=None):
        """تحديث نموذج المستويات"""
        try:
            if not self.db or not self.db.isOpen():
                return
            
            print(f"🔍 البحث عن المستويات للسنة الدراسية: {self.current_academic_year}")
            
            # إنشاء نموذج البيانات
            self.levels_model = QSqlQueryModel()
            
            # بناء الاستعلام
            query_text = """
                SELECT DISTINCT المستوى
                FROM اللوائح
                WHERE السنة_الدراسية = ?
            """
            
            if guard_filter and guard_filter != "الكل":
                query_text += " AND الحراسة = ?"
            
            query_text += " ORDER BY المستوى"
            
            query = QSqlQuery(db=self.db)
            query.prepare(query_text)
            query.bindValue(0, self.current_academic_year)
            
            if guard_filter and guard_filter != "الكل":
                query.bindValue(1, guard_filter)
            
            print("🔄 تجربة استعلام المستويات رقم 1")
            
            if query.exec_():
                levels_found = 0
                while query.next():
                    level = query.value(0)
                    if level:
                        print(f"   ✅ تم العثور على المستوى: {level}")
                        levels_found += 1
                
                if levels_found > 0:
                    print(f"✅ تم العثور على {levels_found} مستوى في الاستعلام رقم 1")
                    # إعادة تشغيل الاستعلام لتحديث النموذج
                    query.exec_()
                    self.levels_model.setQuery(query)
                else:
                    print("⚠️ لم يتم العثور على مستويات في الاستعلام رقم 1")
            else:
                print(f"❌ فشل الاستعلام رقم 1: {query.lastError().text()}")
                
        except Exception as e:
            print(f"❌ خطأ في تحديث نموذج المستويات: {e}")
    
    def update_classes_model(self, selected_level=None):
        """تحديث نموذج الأقسام"""
        try:
            if not self.db or not self.db.isOpen() or not selected_level:
                return
                
            print(f"🔍 البحث عن الأقسام للمستوى: {selected_level}")
            
            # إنشاء نموذج البيانات
            self.classes_model = QSqlQueryModel()
            
            query = QSqlQuery(db=self.db)
            query.prepare("""
                SELECT DISTINCT القسم
                FROM اللوائح
                WHERE المستوى = ? AND السنة_الدراسية = ?
                ORDER BY القسم
            """)
            query.bindValue(0, selected_level)
            query.bindValue(1, self.current_academic_year)
            
            print("🔄 تجربة استعلام الأقسام رقم 1")
            
            if query.exec_():
                classes_list = []
                while query.next():
                    class_name = query.value(0)
                    if class_name:
                        print(f"   ✅ تم العثور على القسم: {class_name}")
                        classes_list.append(class_name)
                
                if classes_list:
                    print(f"✅ تم العثور على {len(classes_list)} قسم في الاستعلام رقم 1")
                    # إعادة تشغيل الاستعلام لتحديث النموذج
                    query.exec_()
                    self.classes_model.setQuery(query)
                    print(f"✅ إجمالي الأقسام الموجودة: {len(classes_list)}")
                    
                    # إرسال قائمة الأقسام إلى JavaScript
                    self.send_classes_data_to_js(classes_list)
                    
                    # تحديد القسم الأول تلقائياً
                    if classes_list:
                        first_class = classes_list[0]
                        self.selected_class = first_class
                        self.update_lists_model(first_class)
                else:
                    print("⚠️ لم يتم العثور على أقسام")
                    # مسح قائمة الأقسام
                    self.send_classes_data_to_js([])
            else:
                print(f"❌ فشل استعلام الأقسام: {query.lastError().text()}")
                
        except Exception as e:
            print(f"❌ خطأ في تحديث نموذج الأقسام: {e}")
    
    def send_classes_data_to_js(self, classes_list):
        """إرسال قائمة الأقسام إلى JavaScript"""
        try:
            # إرسال البيانات كـ JSON
            json_data = json.dumps(classes_list, ensure_ascii=False)
            js_code = f"""
                if (typeof updateClassesList === 'function') {{
                    updateClassesList({json_data});
                }}
            """
            self.web_view.page().runJavaScript(js_code)
            print("📤 إرسال قائمة الأقسام إلى JavaScript...")
            
        except Exception as e:
            print(f"❌ خطأ في إرسال قائمة الأقسام: {e}")
    
    def update_lists_model(self, selected_class=None):
        """تحديث نموذج قائمة التلاميذ"""
        try:
            if not self.db or not self.db.isOpen() or not selected_class:
                return
                
            self.selected_class = selected_class
            
            print(f"🔍 البحث عن التلاميذ في:")
            print(f"   السنة الدراسية: {self.current_academic_year}")
            print(f"   القسم: {selected_class}")
            print(f"   مصدر البيانات: جدول 'اللوائح' + جدول 'السجل_العام'")
            
            # إنشاء نموذج البيانات
            self.lists_model = QSqlQueryModel()
            
            query = QSqlQuery(db=self.db)
            query.prepare("""
                SELECT 
                    ل.رت as رت,
                    ل.الاسم_والنسب as الاسم,
                    ل.الرمز as الرمز,
                    COALESCE(س.السماح, 0) as السماح,
                    COALESCE(س.التأخر, 0) as التأخر,
                    COALESCE(س.عدد_المخالفات, 0) as المخالفات
                FROM اللوائح ل
                LEFT JOIN السجل_العام س ON ل.الرمز = س.الرمز
                WHERE ل.القسم = ? AND ل.السنة_الدراسية = ?
                ORDER BY ل.رت
            """)
            query.bindValue(0, selected_class)
            query.bindValue(1, self.current_academic_year)
            
            if query.exec_():
                print("✅ تم تنفيذ الاستعلام بنجاح")
                students_found = 0
                while query.next():
                    rt = query.value(0)
                    name = query.value(1)
                    code = query.value(2)
                    print(f"   ✅ تم العثور على: {name} - {code} (رت: {rt})")
                    students_found += 1
                
                if students_found > 0:
                    print(f"✅ تم العثور على {students_found} تلميذ")
                    # إعادة تشغيل الاستعلام لتحديث النموذج
                    query.exec_()
                    self.lists_model.setQuery(query)
                    
                    # إرسال البيانات إلى JavaScript
                    self.send_students_data_to_js()
                else:
                    print("⚠️ لم يتم العثور على تلاميذ")
            else:
                print(f"❌ فشل استعلام التلاميذ: {query.lastError().text()}")
                
        except Exception as e:
            print(f"❌ خطأ في تحديث نموذج التلاميذ: {e}")
    
    def send_students_data_to_js(self):
        """إرسال بيانات التلاميذ إلى JavaScript"""
        try:
            if not self.lists_model:
                return
                
            students_data = []
            for row in range(self.lists_model.rowCount()):
                student = {
                    'rt': self.lists_model.data(self.lists_model.index(row, 0)),
                    'name': self.lists_model.data(self.lists_model.index(row, 1)),
                    'code': self.lists_model.data(self.lists_model.index(row, 2)),
                    'entry': self.lists_model.data(self.lists_model.index(row, 3)),
                    'late': self.lists_model.data(self.lists_model.index(row, 4)),
                    'violations': self.lists_model.data(self.lists_model.index(row, 5))
                }
                students_data.append(student)
            
            # إرسال البيانات كـ JSON
            json_data = json.dumps(students_data, ensure_ascii=False)
            self.studentsUpdated.emit(json_data)
            print("📤 إرسال إشارة studentsUpdated...")
            
        except Exception as e:
            print(f"❌ خطأ في إرسال بيانات التلاميذ: {e}")
    
    def on_students_updated(self, data):
        """معالج إشارة تحديث التلاميذ"""
        try:
            js_code = f"""
                if (typeof updateStudentsTable === 'function') {{
                    updateStudentsTable({data});
                }}
            """
            self.web_view.page().runJavaScript(js_code)
        except Exception as e:
            print(f"خطأ في معالج تحديث التلاميذ: {e}")
    
    def on_data_updated(self, data):
        """معالج إشارة تحديث البيانات"""
        try:
            js_code = f"""
                if (typeof handleDataUpdate === 'function') {{
                    handleDataUpdate({data});
                }}
            """
            self.web_view.page().runJavaScript(js_code)
        except Exception as e:
            print(f"خطأ في معالج تحديث البيانات: {e}")
    
    @pyqtSlot(str)
    def on_level_selected(self, level):
        """معالج اختيار المستوى"""
        try:
            print(f"تم اختيار المستوى: {level}")
            self.update_classes_model(level)
        except Exception as e:
            print(f"خطأ في معالج اختيار المستوى: {e}")
    
    @pyqtSlot(str)
    def on_class_selected(self, class_name):
        """معالج اختيار القسم"""
        try:
            print(f"تم اختيار القسم: {class_name}")
            self.update_lists_model(class_name)
        except Exception as e:
            print(f"خطأ في معالج اختيار القسم: {e}")
    
    @pyqtSlot(str)
    def on_entry_button_clicked(self, selected_students_json):
        """معالج زر ورقة الدخول"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_entry_form(selected_students, section)
                
                if result.get("success"):
                    # إعادة تحميل البيانات
                    self.reload_all_data()
                    # إرسال إشارة مسح التحديدات
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    print("✅ تم إرسال إشارة مسح التحديدات بنجاح")
                    
        except Exception as e:
            print(f"خطأ في معالج زر ورقة الدخول: {e}")
    
    @pyqtSlot(str)
    def on_late_button_clicked(self, selected_students_json):
        """معالج زر ورقة التأخر"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_late_form(selected_students, section)
                
                if result.get("success"):
                    # إعادة تحميل البيانات
                    self.reload_all_data()
                    # إرسال إشارة مسح التحديدات
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    print("✅ تم إرسال إشارة مسح التحديدات بنجاح")
                    
        except Exception as e:
            print(f"خطأ في معالج زر ورقة التأخر: {e}")
    
    @pyqtSlot(str)
    def on_doctor_visit_button_clicked(self, selected_students_json):
        """معالج زر زيارة الطبيب"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_doctor_visit_form(selected_students, section)
                
                if result.get("success"):
                    # إرسال إشارة مسح التحديدات
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    
        except Exception as e:
            print(f"خطأ في معالج زر زيارة الطبيب: {e}")
    
    @pyqtSlot(str)
    def on_guidance_button_clicked(self, selected_students_json):
        """معالج زر ورقة التوجيه"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_guidance_form(selected_students, section)
                
                if result.get("success"):
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    
        except Exception as e:
            print(f"خطأ في معالج زر ورقة التوجيه: {e}")
    
    @pyqtSlot(str)
    def on_permission_button_clicked(self, selected_students_json):
        """معالج زر ورقة الاستئذان"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_permission_form(selected_students, section)
                
                if result.get("success"):
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    
        except Exception as e:
            print(f"خطأ في معالج زر ورقة الاستئذان: {e}")
    
    @pyqtSlot(str)
    def on_secret_codes_button_clicked(self, selected_students_json):
        """معالج زر الرموز السرية"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                result = self.functions.handle_secret_codes(selected_students)
                
                if result.get("success"):
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    
        except Exception as e:
            print(f"خطأ في معالج زر الرموز السرية: {e}")
    
    @pyqtSlot(str)
    def on_school_certificates_button_clicked(self, selected_students_json):
        """معالج زر طلبات الشهادات المدرسية"""
        try:
            if self.functions:
                selected_students = json.loads(selected_students_json)
                section = self.selected_class or ""
                result = self.functions.handle_school_certificates(selected_students, section)
                
                if result.get("success"):
                    self.dataUpdated.emit('{"action": "clearSelections", "timestamp": "' + 
                                        datetime.datetime.now().strftime('%H:%M:%S') + '"}')
                    
        except Exception as e:
            print(f"خطأ في معالج زر طلبات الشهادات: {e}")
    
    @pyqtSlot()
    def on_export_button_clicked(self):
        """معالج زر التصدير"""
        try:
            if self.functions:
                result = self.functions.handle_export_data(self.selected_class)
        except Exception as e:
            print(f"خطأ في معالج زر التصدير: {e}")
    
    def reload_all_data(self):
        """إعادة تحميل جميع البيانات"""
        try:
            print("🔄 استدعاء reload_all_data لإرسال إشارة مسح التحديدات...")
            if self.selected_class:
                print(f"🔄 إعادة تحميل بيانات القسم: {self.selected_class}")
                self.update_lists_model(self.selected_class)
            print("✅ انتهت عملية reload_all_data بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إعادة تحميل البيانات: {e}")

    def get_complete_html(self):
        """إنشاء محتوى HTML كامل للنافذة"""
        return f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة الحراسة العامة - الإصدار الحديث</title>
    <style>
        {self.get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        {self.get_header_html()}
        {self.get_main_content_html()}
    </div>
    
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        {self.get_javascript_code()}
    </script>
</body>
</html>
        """
    
    def get_css_styles(self):
        """إرجاع أنماط CSS للنافذة"""
        return """
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header-title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .control-label {
            font-weight: bold;
            color: #34495e;
            font-size: 14px;
        }
        
        .control-input {
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }
        
        .buttons-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .action-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
            background: linear-gradient(45deg, #2980b9, #1abc9c);
        }
        
        .action-button:active {
            transform: translateY(0);
        }
        
        .action-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: 600px;
        }
        
        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .levels-panel,
        .classes-panel {
            flex: 1;
        }
        
        .data-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .data-list {
            flex: 1;
            overflow-y: auto;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
        }
        
        .data-item {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .data-item:hover {
            background: #ebf3fd;
            transform: translateX(-2px);
        }
        
        .data-item.selected {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            font-weight: bold;
        }
        
        .students-panel {
            /* إزالة grid-column لأن التخطيط تغير */
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .students-table th {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .students-table td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            transition: all 0.3s ease;
        }
        
        .students-table tr:hover {
            background: #f8f9fa;
        }
        
        .students-table tr.selected {
            background: #e3f2fd;
        }
        
        .checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #3498db;
        }
        
        .student-code {
            color: #3498db;
            font-weight: bold;
            cursor: pointer;
            text-decoration: underline;
        }
        
        .student-code:hover {
            color: #2980b9;
        }
        
        .stats-badge {
            background: #ecf0f1;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .stats-entry {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .stats-late {
            background: #ffeaa7;
            color: #e17055;
        }
        
        .stats-violations {
            background: #fab1a0;
            color: #e74c3c;
        }
        
        .alert {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            min-width: 300px;
            text-align: center;
            display: none;
        }
        
        .alert.show {
            display: block;
            animation: alertShow 0.3s ease;
        }
        
        @keyframes alertShow {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        .alert-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .alert-message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .alert-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .alert-button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .alert-button.primary {
            background: #3498db;
            color: white;
        }
        
        .alert-button.primary:hover {
            background: #2980b9;
        }
        
        .alert-button.secondary {
            background: #95a5a6;
            color: white;
        }
        
        .alert-button.secondary:hover {
            background: #7f8c8d;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .overlay.show {
            display: block;
        }
        
        /* تحسينات الاستجابة */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 250px 1fr;
            }
        }
        
        @media (max-width: 900px) {
            .main-content {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
                height: auto;
            }
            
            .left-panel {
                flex-direction: row;
                height: 250px;
            }
            
            .levels-panel,
            .classes-panel {
                flex: 1;
            }
            
            .controls-section {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .buttons-section {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
        }
        
        /* شريط التمرير المخصص */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #3498db;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #2980b9;
        }
        """
    
    def get_header_html(self):
        """إرجاع HTML للرأس"""
        return """
        <div class="header">
            <h1 class="header-title">🏫 نظام الحراسة العامة - الإصدار الحديث</h1>
            
            <div class="controls-section">
                <div class="control-group">
                    <label class="control-label">حراسة رقم:</label>
                    <select class="control-input" id="guardSelect">
                        <option value="الكل">جميع الحراسات</option>
                        <option value="1">حراسة رقم 1</option>
                        <option value="2">حراسة رقم 2</option>
                        <option value="3">حراسة رقم 3</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">التاريخ:</label>
                    <input type="date" class="control-input" id="dateInput">
                </div>
                
                <div class="control-group">
                    <label class="control-label">الوقت:</label>
                    <input type="time" class="control-input" id="timeInput">
                </div>
                
                <div class="control-group">
                    <label class="control-label">استخدام الوقت الحالي:</label>
                    <input type="checkbox" class="checkbox" id="useCurrentTime" checked>
                </div>
            </div>
            
            <div class="buttons-section">
                <button class="action-button" onclick="handleEntryForm()">📝 ورقة الدخول</button>
                <button class="action-button" onclick="handleLateForm()">⏰ ورقة التأخر</button>
                <button class="action-button" onclick="handleDoctorVisit()">🏥 زيارة الطبيب</button>
                <button class="action-button" onclick="handleGuidanceForm()">📋 ورقة التوجيه</button>
                <button class="action-button" onclick="handlePermissionForm()">🔖 ورقة الاستئذان</button>
                <button class="action-button" onclick="handleSecretCodes()">🔐 الرموز السرية</button>
                <button class="action-button" onclick="handleSchoolCertificates()">📜 طلبات الشهادات</button>
                <button class="action-button" onclick="handleExport()">📊 تصدير البيانات</button>
            </div>
        </div>
        """
    
    def get_main_content_html(self):
        """إرجاع HTML للمحتوى الرئيسي"""
        return """
        <div class="main-content">
            <div class="left-panel">
                <div class="data-panel levels-panel">
                    <h3 class="panel-title">المستويات الدراسية</h3>
                    <div class="data-list" id="levelsList">
                        <div class="data-item" onclick="selectLevel(this, 'جذع مشترك علمي')">جذع مشترك علمي</div>
                        <div class="data-item" onclick="selectLevel(this, 'جذع مشترك آداب')">جذع مشترك آداب</div>
                        <div class="data-item" onclick="selectLevel(this, 'الأولى باكالوريا علوم')">الأولى باكالوريا علوم</div>
                        <div class="data-item" onclick="selectLevel(this, 'الثانية باكالوريا علوم')">الثانية باكالوريا علوم</div>
                    </div>
                </div>
                
                <div class="data-panel classes-panel">
                    <h3 class="panel-title">الأقسام</h3>
                    <div class="data-list" id="classesList">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
            
            <div class="data-panel students-panel">
                <h3 class="panel-title">قائمة التلاميذ</h3>
                <div class="data-list">
                    <table class="students-table" id="studentsTable">
                        <thead>
                            <tr>
                                <th width="50">تحديد</th>
                                <th width="60">ر.ت</th>
                                <th width="120">الرمز</th>
                                <th>الاسم والنسب</th>
                                <th width="60">دخول</th>
                                <th width="60">تأخر</th>
                                <th width="60">مخالفات</th>
                            </tr>
                        </thead>
                        <tbody id="studentsTableBody">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- نافذة التنبيهات -->
        <div class="overlay" id="alertOverlay"></div>
        <div class="alert" id="alertDialog">
            <div class="alert-title" id="alertTitle"></div>
            <div class="alert-message" id="alertMessage"></div>
            <div class="alert-buttons" id="alertButtons"></div>
        </div>
        """
    
    def get_javascript_code(self):
        """إرجاع كود JavaScript للنافذة"""
        return """
        let pyguard = null;
        let selectedStudents = [];
        let currentLevelData = [];
        let currentClassData = [];
        let currentStudentsData = [];
        
        // تهيئة قناة التواصل مع Python
        new QWebChannel(qt.webChannelTransport, function(channel) {
            pyguard = channel.objects.pyguard;
            console.log('تم تهيئة قناة التواصل مع Python بنجاح');
            
            // تهيئة التاريخ والوقت الحاليين
            initializeDateTime();
        });
        
        function initializeDateTime() {
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const timeStr = now.toTimeString().split(' ')[0].substring(0, 5);
            
            document.getElementById('dateInput').value = dateStr;
            document.getElementById('timeInput').value = timeStr;
        }
        
        function selectLevel(element, level) {
            // إزالة التحديد من العناصر الأخرى
            document.querySelectorAll('#levelsList .data-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // تحديد العنصر الحالي
            element.classList.add('selected');
            
            // استدعاء Python لتحديث الأقسام
            if (pyguard) {
                pyguard.on_level_selected(level);
            }
            
            console.log('تم اختيار المستوى:', level);
        }
        
        function selectClass(element, className) {
            // إزالة التحديد من العناصر الأخرى
            document.querySelectorAll('#classesList .data-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // تحديد العنصر الحالي
            element.classList.add('selected');
            
            // استدعاء Python لتحديث التلاميذ
            if (pyguard) {
                pyguard.on_class_selected(className);
            }
            
            console.log('تم اختيار القسم:', className);
        }
        
        function updateClassesList(classesData) {
            console.log('تحديث قائمة الأقسام:', classesData);
            const classesList = document.getElementById('classesList');
            classesList.innerHTML = '';
            
            if (classesData && classesData.length > 0) {
                classesData.forEach(className => {
                    const classItem = document.createElement('div');
                    classItem.className = 'data-item';
                    classItem.textContent = className;
                    classItem.onclick = () => selectClass(classItem, className);
                    classesList.appendChild(classItem);
                });
                
                // اختيار القسم الأول تلقائياً
                if (classesData.length > 0) {
                    const firstClass = classesList.firstElementChild;
                    if (firstClass) {
                        selectClass(firstClass, classesData[0]);
                    }
                }
            }
        }
        
        function updateStudentsTable(studentsData) {
            console.log('تحديث جدول التلاميذ:', studentsData);
            currentStudentsData = JSON.parse(studentsData);
            
            const tbody = document.getElementById('studentsTableBody');
            tbody.innerHTML = '';
            
            currentStudentsData.forEach((student, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="checkbox" id="student_${index}" onchange="toggleStudentSelection(${index})"></td>
                    <td>${student.rt || ''}</td>
                    <td><span class="student-code" onclick="showStudentCard('${student.code}')">${student.code || ''}</span></td>
                    <td style="text-align: right; padding-right: 10px;">${student.name || ''}</td>
                    <td><span class="stats-badge stats-entry">${student.entry || 0}</span></td>
                    <td><span class="stats-badge stats-late">${student.late || 0}</span></td>
                    <td><span class="stats-badge stats-violations">${student.violations || 0}</span></td>
                `;
                tbody.appendChild(row);
            });
            
            // مسح التحديدات السابقة
            selectedStudents = [];
        }
        
        function toggleStudentSelection(index) {
            const checkbox = document.getElementById(`student_${index}`);
            const row = checkbox.closest('tr');
            
            if (checkbox.checked) {
                selectedStudents.push(currentStudentsData[index]);
                row.classList.add('selected');
            } else {
                const studentIndex = selectedStudents.findIndex(s => s.code === currentStudentsData[index].code);
                if (studentIndex > -1) {
                    selectedStudents.splice(studentIndex, 1);
                }
                row.classList.remove('selected');
            }
            
            console.log('التلاميذ المحددون:', selectedStudents.length);
        }
        
        function showStudentCard(studentCode) {
            showAlert('بطاقة التلميذ', `عرض بطاقة التلميذ: ${studentCode}`, 'info');
        }
        
        function getSelectedStudents() {
            return selectedStudents;
        }
        
        function clearSelections() {
            selectedStudents = [];
            document.querySelectorAll('#studentsTable input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.querySelectorAll('#studentsTable tr').forEach(row => {
                row.classList.remove('selected');
            });
            console.log('تم مسح جميع التحديدات');
        }
        
        function handleDataUpdate(data) {
            const updateData = JSON.parse(data);
            if (updateData.action === 'clearSelections') {
                setTimeout(() => {
                    clearSelections();
                }, 500);
            }
        }
        
        // وظائف الأزرار
        function handleEntryForm() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإصدار ورقة الدخول', 'warning');
                return;
            }
            
            const studentsInfo = selectedStudents.map(s => `${s.name} (${s.code})`).join('\\n');
            showConfirmAlert(
                'تأكيد ورقة الدخول',
                `هل تريد إصدار ورقة دخول للتلاميذ التالية أسماؤهم؟\\n\\n${studentsInfo}`,
                () => {
                    if (pyguard) {
                        pyguard.on_entry_button_clicked(JSON.stringify(selectedStudents));
                    }
                }
            );
        }
        
        function handleLateForm() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإصدار ورقة التأخر', 'warning');
                return;
            }
            
            const studentsInfo = selectedStudents.map(s => `${s.name} (${s.code})`).join('\\n');
            showConfirmAlert(
                'تأكيد ورقة التأخر',
                `هل تريد إصدار ورقة تأخر للتلاميذ التالية أسماؤهم؟\\n\\n${studentsInfo}`,
                () => {
                    if (pyguard) {
                        pyguard.on_late_button_clicked(JSON.stringify(selectedStudents));
                    }
                }
            );
        }
        
        function handleDoctorVisit() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإصدار ورقة زيارة الطبيب', 'warning');
                return;
            }
            
            const studentsInfo = selectedStudents.map(s => `${s.name} (${s.code})`).join('\\n');
            showConfirmAlert(
                'تأكيد زيارة الطبيب',
                `هل تريد إصدار ورقة زيارة الطبيب للتلاميذ التالية أسماؤهم؟\\n\\n${studentsInfo}`,
                () => {
                    if (pyguard) {
                        pyguard.on_doctor_visit_button_clicked(JSON.stringify(selectedStudents));
                    }
                }
            );
        }
        
        function handleGuidanceForm() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإصدار ورقة التوجيه', 'warning');
                return;
            }
            
            if (pyguard) {
                pyguard.on_guidance_button_clicked(JSON.stringify(selectedStudents));
            }
        }
        
        function handlePermissionForm() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإصدار ورقة الاستئذان', 'warning');
                return;
            }
            
            if (pyguard) {
                pyguard.on_permission_button_clicked(JSON.stringify(selectedStudents));
            }
        }
        
        function handleSecretCodes() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لطباعة الرموز السرية', 'warning');
                return;
            }
            
            if (pyguard) {
                pyguard.on_secret_codes_button_clicked(JSON.stringify(selectedStudents));
            }
        }
        
        function handleSchoolCertificates() {
            if (selectedStudents.length === 0) {
                showAlert('تنبيه', 'الرجاء تحديد تلميذ واحد على الأقل لإضافة طلب شهادة مدرسية', 'warning');
                return;
            }
            
            if (pyguard) {
                pyguard.on_school_certificates_button_clicked(JSON.stringify(selectedStudents));
            }
        }
        
        function handleExport() {
            if (pyguard) {
                pyguard.on_export_button_clicked();
            }
        }
        
        // وظائف التنبيهات
        function showAlert(title, message, type = 'info') {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            document.getElementById('alertButtons').innerHTML = `
                <button class="alert-button primary" onclick="closeAlert()">موافق</button>
            `;
            
            document.getElementById('alertOverlay').classList.add('show');
            document.getElementById('alertDialog').classList.add('show');
        }
        
        function showConfirmAlert(title, message, onConfirm) {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            document.getElementById('alertButtons').innerHTML = `
                <button class="alert-button primary" onclick="confirmAction(); closeAlert();">تأكيد</button>
                <button class="alert-button secondary" onclick="closeAlert()">إلغاء</button>
            `;
            
            window.pendingConfirmAction = onConfirm;
            
            document.getElementById('alertOverlay').classList.add('show');
            document.getElementById('alertDialog').classList.add('show');
        }
        
        function confirmAction() {
            if (window.pendingConfirmAction) {
                window.pendingConfirmAction();
                window.pendingConfirmAction = null;
            }
        }
        
        function closeAlert() {
            document.getElementById('alertOverlay').classList.remove('show');
            document.getElementById('alertDialog').classList.remove('show');
            window.pendingConfirmAction = null;
        }
        
        // إغلاق التنبيه عند النقر على الخلفية
        document.getElementById('alertOverlay').addEventListener('click', closeAlert);
        
        console.log('تم تحميل JavaScript بنجاح');
        """

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    app = QApplication(sys.argv)
    
    # إعداد الترميز
    app.setOrganizationName("SchoolSystem")
    app.setApplicationName("GuardWindow-Modern")
    
    window = GuardWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
